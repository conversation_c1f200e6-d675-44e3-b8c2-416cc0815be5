name: web3signer-minimal
services:
  # Minimal Web3Signer setup with slashing protection disabled
  web3signer:
    image: consensys/web3signer:latest
    container_name: web3signer-minimal
    ports:
      - "9000:9000"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9000
      --slashing-protection-enabled=false
      --network=sepolia
      eth2
    restart: unless-stopped

  # Test Web3Signer with curl
  web3signer-test:
    image: curlimages/curl:latest
    container_name: web3signer-curl-test
    depends_on:
      - web3signer
    command: >
      sh -c '
        echo "🧪 Testing Web3Signer endpoints...";
        sleep 10;
        echo "";
        echo "1. Testing upcheck:";
        curl -f http://web3signer:9000/upcheck || echo "Upcheck failed";
        echo "";
        echo "2. Testing public keys endpoint:";
        curl -f http://web3signer:9000/api/v1/eth2/publicKeys || echo "Public keys endpoint failed";
        echo "";
        echo "3. Testing health endpoint:";
        curl -f http://web3signer:9000/healthcheck || echo "Health endpoint failed";
        echo "";
        echo "✅ Web3Signer tests completed!";
        sleep 30;
      '
    profiles:
      - test

volumes:
  web3signer_keys:
