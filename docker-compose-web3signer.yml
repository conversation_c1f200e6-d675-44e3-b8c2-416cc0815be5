name: aztec-web3signer-testnet

services:
  # Aztec CLI service for testing Web3Signer commands
  aztec-cli:
    image: ${AZTEC_IMAGE}
    container_name: aztec-cli
    environment:
      NODE_NO_WARNINGS: ${NODE_NO_WARNINGS}
      LOG_LEVEL: ${LOG_LEVEL}
    volumes:
      - .:/workspace
      - aztec_data:${AZTEC_DATA_PATH}
    working_dir: /workspace
    networks:
      - aztec-network
    stdin_open: true
    tty: true
    entrypoint: []
    command: >
      sh -c '
        echo "🎯 Aztec CLI with Web3Signer Integration";
        echo "==========================================";
        echo "";
        echo "Environment: ${NETWORK}";
        echo "L1 RPC: ${L1_RPC}";
        echo "L1 Consensus: ${L1_CONSENSUS_HOST_URL}";
        echo "Web3Signer URL: ${WEB3SIGNER_URL}";
        echo "";
        echo "Testing Web3Signer integration:";
        node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --help | grep -i web3signer || echo "✅ Web3Signer integration found!";
        echo "";
        echo "Available CLI commands:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js --help";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js --help";
        echo "";
        echo "Web3Signer account creation test:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --type web3signer --web3signer-url ${WEB3SIGNER_URL} --web3signer-key-id test-key";
        echo "";
        echo "🚀 CLI Ready for testing!";
        tail -f /dev/null
      '
    profiles:
      - cli

  # Aztec Node with Web3Signer integration
  aztec-node:
    image: ${AZTEC_IMAGE}
    container_name: aztec-node
    environment:
      NODE_NO_WARNINGS: ${NODE_NO_WARNINGS}
      LOG_LEVEL: ${LOG_LEVEL}
      NETWORK: ${NETWORK}
      PUBLIC_IP_ADDRESS: ${PUBLIC_IP_ADDRESS}
      VALIDATOR_P2P_PORT: ${VALIDATOR_P2P_PORT}
      L1_RPC: ${L1_RPC}
      L1_CONSENSUS_HOST_URL: ${L1_CONSENSUS_HOST_URL}
      VALIDATOR_PRIVATE_KEY: ${VALIDATOR_PRIVATE_KEY}
      COINBASE: ${COINBASE}
      BLOB_SINK_URL: ${BLOB_SINK_URL}
      GOVERNANCE_PROPOSER_PAYLOAD_ADDRESS: ${GOVERNANCE_PROPOSER_PAYLOAD_ADDRESS}
      P2P_MAX_TX_POOL_SIZE: ${P2P_MAX_TX_POOL_SIZE}
      SENTINEL_ENABLED: ${SENTINEL_ENABLED}
      SENTINEL_HISTORY_LENGTH_IN_EPOCHS: ${SENTINEL_HISTORY_LENGTH_IN_EPOCHS}
      WEB3SIGNER_URL: ${WEB3SIGNER_URL}
      WEB3SIGNER_ENABLED: ${WEB3SIGNER_ENABLED}
    ports:
      - "${PXE_PORT}:8080"  # PXE
      - "${VALIDATOR_P2P_PORT}:${VALIDATOR_P2P_PORT}"  # P2P
    volumes:
      - aztec_data:${AZTEC_DATA_PATH}
    networks:
      - aztec-network
    entrypoint: []
    command: >
      node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js
      start --network ${NETWORK}
      --p2p.p2pIp ${PUBLIC_IP_ADDRESS}
      --p2p.p2pPort ${VALIDATOR_P2P_PORT}
      --l1-rpc-urls ${L1_RPC}
      --l1-consensus-host-urls ${L1_CONSENSUS_HOST_URL}
      --sequencer.validatorPrivateKey ${VALIDATOR_PRIVATE_KEY}
      --web3signer-url ${WEB3SIGNER_URL}
      --node --archiver --sequencer --pxe
    depends_on:
      - web3signer
    profiles:
      - node

  # Web3Signer service
  web3signer:
    image: ${WEB3SIGNER_IMAGE}
    container_name: web3signer
    environment:
      WEB3SIGNER_HTTP_LISTEN_HOST: "0.0.0.0"
      WEB3SIGNER_HTTP_LISTEN_PORT: "9000"
    ports:
      - "9000:9000"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config:ro
    networks:
      - aztec-network
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9000
      --logging=INFO
      eth2
    profiles:
      - web3signer
      - node

networks:
  aztec-network:
    driver: bridge

volumes:
  aztec_data:
  web3signer_keys:
