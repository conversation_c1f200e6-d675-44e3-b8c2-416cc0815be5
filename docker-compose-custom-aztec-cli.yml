name: custom-aztec-cli
services:
  # Custom Aztec CLI service using your Web3Signer integrated image
  aztec-cli:
    image: aztecprotocol/aztec:latest  # Replace with your custom image name
    container_name: aztec-cli-custom
    environment:
      NODE_NO_WARNINGS: 1
    volumes:
      - .:/workspace
      - aztec_data:/root/.aztec
    working_dir: /workspace
    network_mode: host
    stdin_open: true
    tty: true
    entrypoint: >
      sh -c '
        echo "🎯 Custom Aztec CLI with Web3Signer Integration";
        echo "===============================================";
        echo "";
        echo "Available commands:";
        echo "  aztec --help              # Main CLI";
        echo "  aztec-wallet --help       # Wallet CLI";
        echo "  aztec-nargo --help        # Noir compiler";
        echo "";
        echo "Web3Signer integration test:";
        node /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --help | grep -i web3signer || echo "Web3Signer integration found!";
        echo "";
        echo "🚀 Ready for commands! Use: docker compose exec aztec-cli <command>";
        echo "Example: docker compose exec aztec-cli node /usr/src/yarn-project/aztec/dest/bin/index.js --help";
        echo "";
        tail -f /dev/null
      '

  # Sandbox service (optional - for full testing)
  aztec-sandbox:
    image: aztecprotocol/aztec:latest  # Replace with your custom image name
    container_name: aztec-sandbox-custom
    environment:
      NODE_NO_WARNINGS: 1
      LOG_LEVEL: info
      # Add required environment variables for sandbox
      PRIVATE_KEY: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
      MNEMONIC: "test test test test test test test test test test test junk"
      COINBASE_ADDRESS: "******************************************"
      FEE_RECIPIENT_ADDRESS: "******************************************"
    ports:
      - "8080:8080"  # PXE
      - "8545:8545"  # Ethereum
    volumes:
      - aztec_data:/root/.aztec
    profiles:
      - sandbox  # Only start with: docker compose --profile sandbox up
    command: >
      sh -c '
        echo "🏗️  Starting Custom Aztec Sandbox with Web3Signer...";
        node /usr/src/yarn-project/aztec/dest/bin/index.js start --sandbox
      '

volumes:
  aztec_data:
