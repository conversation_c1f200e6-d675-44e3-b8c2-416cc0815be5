#!/bin/bash

echo "🚀 Web3Signer Validator Test - Local Devnet"
echo "📋 Testing Web3Signer for Ethereum validator message signing"

# Configuration
ETHEREUM_PORT=18545
WEB3SIGNER_PORT=19000
AZTEC_NODE_PORT=18999
AZTEC_PXE_PORT=18080

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up..."
    docker compose -f docker-compose.validator-test.yml down -v 2>/dev/null || true
    docker network rm web3signer-validator-test 2>/dev/null || true
    rm -f /tmp/validator-test-*.log
}

# Set up cleanup trap
trap cleanup EXIT

echo "📦 Creating test environment..."

# Create docker-compose file for validator test
cat > docker-compose.validator-test.yml << 'EOF'
name: web3signer-validator-test
services:
  ethereum:
    image: ghcr.io/foundry-rs/foundry:latest
    platform: linux/amd64
    command:
      - anvil
      - --host
      - "0.0.0.0"
      - --port
      - "8545"
      - --chain-id
      - "31337"
      - --accounts
      - "10"
      - --mnemonic
      - "test test test test test test test test test test test junk"
      - --block-time
      - "1"
      - --gas-limit
      - "********"
    ports:
      - "18545:8545"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8545"]
      interval: 5s
      timeout: 3s
      retries: 10
    networks:
      - validator-test

  web3signer:
    image: consensys/web3signer:latest
    platform: linux/amd64
    command:
      - --key-store-path=/opt/web3signer/keys
      - --http-listen-port=9000
      - --http-listen-host=0.0.0.0
      - --http-cors-origins=*
      - --metrics-enabled=true
      - --metrics-port=9001
      - --logging=INFO
      - eth2
      - --network=minimal
      - --eth1-endpoint=http://ethereum:8545
    ports:
      - "19000:9000"
      - "19001:9001"
    volumes:
      - ./web3signer-keys:/opt/web3signer/keys:ro
    depends_on:
      ethereum:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/upcheck"]
      interval: 5s
      timeout: 3s
      retries: 10
    networks:
      - validator-test

  aztec-node:
    image: aztecprotocol/aztec:latest
    platform: linux/amd64
    environment:
      LOG_LEVEL: info
      L1_CHAIN_ID: 31337
      ETHEREUM_HOSTS: http://ethereum:8545
      AZTEC_PORT: 8999
      DATA_DIRECTORY: /var/lib/aztec
      NODE_NO_WARNINGS: 1
      PROVER_REAL_PROOFS: 0
      PROVER_AGENT_ENABLED: 0
      WEB3SIGNER_URL: http://web3signer:9000
    command: |
      sh -c '
        echo "Waiting for Ethereum and Web3Signer...";
        while ! curl --head --silent http://ethereum:8545 > /dev/null; do 
          echo "Waiting for Ethereum..."; 
          sleep 2; 
        done;
        while ! curl --head --silent http://web3signer:9000/upcheck > /dev/null; do 
          echo "Waiting for Web3Signer..."; 
          sleep 2; 
        done;
        echo "Starting Aztec node with Web3Signer integration...";
        node /usr/src/yarn-project/aztec/dest/bin/index.js start --node --archiver
      '
    ports:
      - "18999:8999"
    depends_on:
      ethereum:
        condition: service_healthy
      web3signer:
        condition: service_healthy
    volumes:
      - aztec-data:/var/lib/aztec
    networks:
      - validator-test

  aztec-pxe:
    image: aztecprotocol/aztec:latest
    platform: linux/amd64
    environment:
      LOG_LEVEL: info
      AZTEC_NODE_URL: http://aztec-node:8999
      AZTEC_PORT: 8080
      PXE_DATA_DIRECTORY: /var/lib/aztec/pxe
      NODE_NO_WARNINGS: 1
      MNEMONIC: "test test test test test test test test test test test junk"
      WEB3SIGNER_URL: http://web3signer:9000
    command: |
      sh -c '
        echo "Waiting for Aztec node...";
        while ! curl --head --silent http://aztec-node:8999 > /dev/null; do 
          echo "Waiting for Aztec node..."; 
          sleep 2; 
        done;
        echo "Starting Aztec PXE with Web3Signer support...";
        node /usr/src/yarn-project/aztec/dest/bin/index.js start --pxe
      '
    ports:
      - "18080:8080"
    depends_on:
      - aztec-node
      - web3signer
    volumes:
      - aztec-pxe-data:/var/lib/aztec
    networks:
      - validator-test

volumes:
  aztec-data:
  aztec-pxe-data:

networks:
  validator-test:
    driver: bridge
EOF

# Create Web3Signer validator keys
echo "🔑 Creating Web3Signer validator keys..."
mkdir -p web3signer-keys

# Create a validator private key (BLS12-381 for Ethereum 2.0)
cat > web3signer-keys/validator-key.yaml << 'EOF'
type: "file-raw"
privateKey: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
EOF

# Create another validator key for testing
cat > web3signer-keys/validator-key-2.yaml << 'EOF'
type: "file-raw" 
privateKey: "0x51eae93f3b7c6c8b6e12b5aa9c671c68157b6f4c8bc18f6c77bd62f8ad7a6867"
EOF

echo "🚀 Starting services..."
docker compose -f docker-compose.validator-test.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Function to wait for service
wait_for_service() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $name..."
    while [ $attempt -le $max_attempts ]; do
        if curl -s --max-time 5 "$url" > /dev/null 2>&1; then
            echo "✅ $name is ready!"
            return 0
        fi
        echo "   Attempt $attempt/$max_attempts - waiting for $name..."
        sleep 2
        attempt=$((attempt + 1))
    done
    echo "❌ $name failed to start after $max_attempts attempts"
    return 1
}

# Wait for all services
wait_for_service "http://localhost:$ETHEREUM_PORT" "Ethereum (Anvil)"
wait_for_service "http://localhost:$WEB3SIGNER_PORT/upcheck" "Web3Signer"
wait_for_service "http://localhost:$AZTEC_NODE_PORT" "Aztec Node"
wait_for_service "http://localhost:$AZTEC_PXE_PORT" "Aztec PXE"

echo "🔍 Checking service status..."
docker compose -f docker-compose.validator-test.yml ps

echo "🧪 Running Web3Signer Validator Tests..."

# Test 1: Check Web3Signer is responding
echo "📡 Test 1: Web3Signer API Health Check"
WS_HEALTH=$(curl -s http://localhost:$WEB3SIGNER_PORT/upcheck)
if [ "$WS_HEALTH" = "OK" ]; then
    echo "✅ Web3Signer health check passed"
else
    echo "❌ Web3Signer health check failed: $WS_HEALTH"
fi

# Test 2: List available validator keys
echo "🗝️  Test 2: List Web3Signer Validator Keys"
WS_KEYS=$(curl -s http://localhost:$WEB3SIGNER_PORT/api/v1/eth2/publicKeys)
echo "Available validator keys: $WS_KEYS"

# Test 3: Test Ethereum connection
echo "🌐 Test 3: Ethereum Connection Test"
ETH_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:$ETHEREUM_PORT)
echo "Ethereum response: $ETH_RESPONSE"

# Test 4: Create Web3Signer account in Aztec
echo "🏗️ Test 4: Create Web3Signer Account in Aztec"
docker run --rm --network web3signer-validator-test_validator-test \
  aztecprotocol/aztec:latest \
  node /usr/src/yarn-project/aztec/dest/bin/index.js create-account \
    --rpc-url http://aztec-pxe:8080 \
    --type web3signer \
    --web3signer-url http://web3signer:9000 \
    --web3signer-key-id validator-key \
    --register-only \
    --json > /tmp/validator-test-account.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Web3Signer account creation successful"
    cat /tmp/validator-test-account.log
else
    echo "❌ Web3Signer account creation failed"
    cat /tmp/validator-test-account.log
fi

echo "✅ Web3Signer Validator Test completed!"
echo ""
echo "📊 Test Summary:"
echo "   - Ethereum node: http://localhost:$ETHEREUM_PORT"
echo "   - Web3Signer: http://localhost:$WEB3SIGNER_PORT"
echo "   - Aztec Node: http://localhost:$AZTEC_NODE_PORT"
echo "   - Aztec PXE: http://localhost:$AZTEC_PXE_PORT"
echo ""
echo "🔧 To interact manually:"
echo "   docker run --rm --network web3signer-validator-test_validator-test aztecprotocol/aztec:latest node /usr/src/yarn-project/aztec/dest/bin/index.js --help"
echo ""
echo "🛑 To stop the test environment:"
echo "   docker compose -f docker-compose.validator-test.yml down -v"
