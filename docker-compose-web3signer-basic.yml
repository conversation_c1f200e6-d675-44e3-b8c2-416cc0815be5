name: web3signer-basic
services:
  # Basic Web3Signer setup - just the essentials
  web3signer:
    image: consensys/web3signer:latest
    container_name: web3signer-basic
    ports:
      - "9000:9000"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9000
      eth2
    restart: unless-stopped

  # Test with a simple HTTP client
  test-client:
    image: alpine:latest
    container_name: web3signer-test-client
    depends_on:
      - web3signer
    command: >
      sh -c '
        apk add --no-cache curl;
        echo "🧪 Waiting for Web3Signer to start...";
        sleep 15;
        echo "";
        echo "Testing Web3Signer endpoints:";
        echo "1. Upcheck:";
        curl -v http://web3signer:9000/upcheck || echo "Failed";
        echo "";
        echo "2. Public keys:";
        curl -v http://web3signer:9000/api/v1/eth2/publicKeys || echo "Failed";
        echo "";
        echo "3. Health check:";
        curl -v http://web3signer:9000/healthcheck || echo "Failed";
        echo "";
        echo "✅ Tests completed!";
        sleep 60;
      '
    profiles:
      - test

volumes:
  web3signer_keys:
