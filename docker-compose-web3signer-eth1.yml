name: web3signer-eth1
services:
  # Web3Signer for ETH1 (SECP256K1) - more suitable for Aztec
  web3signer-eth1:
    image: consensys/web3signer:latest
    container_name: web3signer-eth1
    ports:
      - "9000:9000"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9000
      eth1
    restart: unless-stopped

  # Web3Signer for ETH2 (BLS) - traditional validator signing
  web3signer-eth2:
    image: consensys/web3signer:latest
    container_name: web3signer-eth2
    ports:
      - "9001:9001"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9001
      eth2
    restart: unless-stopped
    profiles:
      - eth2

  # Test both services
  test-both:
    image: alpine:latest
    container_name: web3signer-test-both
    depends_on:
      - web3signer-eth1
    command: >
      sh -c '
        apk add --no-cache curl;
        echo "🧪 Testing Web3Signer ETH1 and ETH2...";
        sleep 15;
        echo "";
        echo "=== Testing ETH1 (port 9000) ===";
        echo "1. ETH1 Upcheck:";
        curl -f http://web3signer-eth1:9000/upcheck || echo "ETH1 upcheck failed";
        echo "";
        echo "2. ETH1 Public keys:";
        curl -f http://web3signer-eth1:9000/api/v1/eth1/publicKeys || echo "ETH1 public keys failed";
        echo "";
        echo "=== Testing ETH2 (port 9001) ===";
        echo "3. ETH2 Upcheck:";
        curl -f http://web3signer-eth2:9001/upcheck || echo "ETH2 upcheck failed (expected if not running)";
        echo "";
        echo "4. ETH2 Public keys:";
        curl -f http://web3signer-eth2:9001/api/v1/eth2/publicKeys || echo "ETH2 public keys failed (expected if not running)";
        echo "";
        echo "✅ Tests completed!";
        sleep 60;
      '
    profiles:
      - test

volumes:
  web3signer_keys:
