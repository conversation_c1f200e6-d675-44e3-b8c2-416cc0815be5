# Web3Signer Validator Test Suite

This test suite validates the Web3Signer integration with Aztec for Ethereum validator message signing in a local devnet environment.

## Overview

The test suite sets up a complete local development environment with:
- **Ethereum node** (Anvil) on port 18545 (avoiding port 8545)
- **Web3Signer** for secure validator key management on port 19000
- **Aztec Node** with Web3Signer integration on port 18999
- **Aztec PXE** for account management on port 18080

## Features Tested

✅ **Web3Signer Health & Connectivity**
- API health checks
- Service availability verification

✅ **Validator Key Management**
- Key discovery and listing
- Multiple validator key support

✅ **Ethereum Validator Operations**
- Block signing (validator duty)
- Attestation signing (consensus participation)
- Message signing with BLS signatures

✅ **Aztec Integration**
- Web3Signer account creation
- CLI integration testing
- Account type registration

✅ **Performance & Reliability**
- Signing performance benchmarks
- Multiple signature operations
- Error handling validation

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js (v18+)
- curl (for health checks)

### Run Complete Test Suite
```bash
# Make scripts executable and run all tests
chmod +x *.sh
./run-validator-test.sh
```

### Run Individual Components

#### 1. Setup Infrastructure Only
```bash
./test-web3signer-validator.sh
```

#### 2. Run Signing Tests Only (after setup)
```bash
node test-validator-signing.js
```

#### 3. Install Dependencies
```bash
npm install
```

## Test Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Ethereum      │    │   Web3Signer    │    │   Aztec Node    │
│   (Anvil)       │◄──►│   Validator     │◄──►│   Integration   │
│   Port: 18545   │    │   Port: 19000   │    │   Port: 18999   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Aztec PXE     │
                    │   Port: 18080   │
                    └─────────────────┘
```

## Configuration

### Port Mapping
- **Ethereum**: 18545 (external) → 8545 (internal)
- **Web3Signer**: 19000 (external) → 9000 (internal)
- **Web3Signer Metrics**: 19001 (external) → 9001 (internal)
- **Aztec Node**: 18999 (external) → 8999 (internal)
- **Aztec PXE**: 18080 (external) → 8080 (internal)

### Validator Keys
The test creates two validator keys:
- `validator-key`: Primary test key
- `validator-key-2`: Secondary test key

## Manual Testing

### Check Service Health
```bash
# Web3Signer health
curl http://localhost:19000/upcheck

# List validator keys
curl http://localhost:19000/api/v1/eth2/publicKeys

# Ethereum block number
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:18545
```

### Create Aztec Account with Web3Signer
```bash
docker run --rm --network web3signer-validator-test_validator-test \
  aztecprotocol/aztec:latest \
  node /usr/src/yarn-project/aztec/dest/bin/index.js create-account \
    --rpc-url http://aztec-pxe:8080 \
    --type web3signer \
    --web3signer-url http://web3signer:9000 \
    --web3signer-key-id validator-key \
    --register-only
```

### Sign Custom Messages
```bash
# Sign a custom message via Web3Signer API
curl -X POST http://localhost:19000/api/v1/eth2/sign \
  -H "Content-Type: application/json" \
  -d '{
    "type": "BLOCK",
    "fork_info": {
      "fork": {
        "previous_version": "0x00000000",
        "current_version": "0x00000000",
        "epoch": "0"
      },
      "genesis_validators_root": "0x0000000000000000000000000000000000000000000000000000000000000000"
    },
    "signingRoot": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
    "identifier": "validator-key"
  }'
```

## Cleanup

### Stop All Services
```bash
docker compose -f docker-compose.validator-test.yml down -v
```

### Clean Up Networks and Volumes
```bash
npm run clean
```

## Troubleshooting

### Services Not Starting
1. Check port availability:
   ```bash
   netstat -tulpn | grep -E "(18545|19000|18999|18080)"
   ```

2. Check Docker logs:
   ```bash
   docker compose -f docker-compose.validator-test.yml logs
   ```

### Web3Signer Connection Issues
1. Verify Web3Signer is running:
   ```bash
   curl http://localhost:19000/upcheck
   ```

2. Check validator keys are loaded:
   ```bash
   curl http://localhost:19000/api/v1/eth2/publicKeys
   ```

### Aztec Integration Issues
1. Check Aztec node connectivity:
   ```bash
   curl http://localhost:18999
   ```

2. Verify PXE is accessible:
   ```bash
   curl http://localhost:18080
   ```

## Expected Output

When successful, you should see:
```
🎉 All tests passed successfully!

📋 What was tested:
   ✅ Web3Signer health and API connectivity
   ✅ Validator key management and retrieval
   ✅ Ethereum block signing (validator duty)
   ✅ Attestation signing (validator consensus)
   ✅ Aztec Web3Signer account integration
   ✅ Performance and reliability testing
   ✅ Multiple validator key support
```

## Integration Points

This test validates the complete integration chain:
1. **Web3Signer** manages validator private keys securely
2. **Ethereum** provides the blockchain context for signing
3. **Aztec** uses Web3Signer for account operations
4. **Validator operations** are performed through secure remote signing

The test ensures that Aztec can successfully integrate with Web3Signer for enterprise-grade validator operations while maintaining security best practices.
