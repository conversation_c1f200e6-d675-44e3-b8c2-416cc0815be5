name: aztec-web3signer-services
services:
  # CLI-only service for testing commands
  aztec-cli:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-cli-web3signer
    environment:
      NODE_NO_WARNINGS: 1
    volumes:
      - .:/workspace
      - aztec_data:/root/.aztec
    working_dir: /workspace
    network_mode: host
    stdin_open: true
    tty: true
    entrypoint: []
    command: >
      sh -c '
        echo "🎯 Aztec CLI with Web3Signer Integration";
        echo "========================================";
        echo "";
        echo "Testing Web3Signer integration:";
        node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --help | grep -i web3signer || echo "✅ Web3Signer integration found!";
        echo "";
        echo "Available CLI commands:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js --help";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js --help";
        echo "";
        echo "Web3Signer account creation test:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --type web3signer --web3signer-url http://localhost:9000 --web3signer-key-id test-key";
        echo "";
        echo "🚀 CLI Ready!";
        tail -f /dev/null
      '
    profiles:
      - cli

  # Full Aztec Sandbox
  aztec-sandbox:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-sandbox-web3signer
    environment:
      NODE_NO_WARNINGS: 1
      LOG_LEVEL: info
      # Required environment variables
      PRIVATE_KEY: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
      MNEMONIC: "test test test test test test test test test test test junk"
      COINBASE_ADDRESS: "******************************************"
      FEE_RECIPIENT_ADDRESS: "******************************************"
    ports:
      - "8080:8080"  # PXE
      - "8545:8545"  # Ethereum
    volumes:
      - aztec_data:/root/.aztec
    entrypoint: []
    command: >
      node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js
      start --sandbox
    profiles:
      - sandbox

  # Aztec Node (for production-like testing)
  aztec-node:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-node-web3signer
    environment:
      NODE_NO_WARNINGS: 1
      LOG_LEVEL: info
      # Network configuration
      NETWORK: "devnet"
      PUBLIC_IP_ADDRESS: "127.0.0.1"
      VALIDATOR_P2P_PORT: "40400"
      L1_RPC: "http://ethereum:8545"
      L1_CONSENSUS_HOST_URL: "http://ethereum:8551"
      VALIDATOR_PRIVATE_KEY: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
      # Required addresses
      COINBASE_ADDRESS: "******************************************"
      FEE_RECIPIENT_ADDRESS: "******************************************"
    ports:
      - "8080:8080"  # PXE
      - "40400:40400"  # P2P
    volumes:
      - aztec_data:/root/.aztec
    depends_on:
      - ethereum
    entrypoint: []
    command: >
      node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js
      start --network devnet --p2p.p2pIp 127.0.0.1
      --p2p.p2pPort 40400 --l1-rpc-urls http://ethereum:8545 --l1-consensus-host-urls http://ethereum:8551
      --sequencer.validatorPrivateKey 0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866
      --node --archiver --sequencer
    profiles:
      - node

  # Local Ethereum node (Anvil)
  ethereum:
    image: ghcr.io/foundry-rs/foundry:latest
    container_name: ethereum-anvil
    ports:
      - "8545:8545"
      - "8551:8551"
    command: >
      anvil
      --host 0.0.0.0
      --port 8545
      --chain-id 31337
      --accounts 10
      --balance 1000000
      --gas-limit ********
      --mnemonic "test test test test test test test test test test test junk"
    profiles:
      - node
      - ethereum

volumes:
  aztec_data:
