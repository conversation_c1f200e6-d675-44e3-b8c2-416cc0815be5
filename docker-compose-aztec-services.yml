name: aztec-web3signer-services
services:
  # CLI-only service for testing commands
  aztec-cli:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-cli-web3signer
    environment:
      NODE_NO_WARNINGS: 1
    volumes:
      - .:/workspace
      - aztec_data:/root/.aztec
    working_dir: /workspace
    network_mode: host
    stdin_open: true
    tty: true
    entrypoint: []
    command: >
      sh -c '
        echo "🎯 Aztec CLI with Web3Signer Integration";
        echo "========================================";
        echo "";
        echo "Testing Web3Signer integration:";
        node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --help | grep -i web3signer || echo "✅ Web3Signer integration found!";
        echo "";
        echo "Available CLI commands:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js --help";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js --help";
        echo "";
        echo "Web3Signer account creation test:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --type web3signer --web3signer-url http://localhost:9000 --web3signer-key-id test-key";
        echo "";
        echo "🚀 CLI Ready!";
        tail -f /dev/null
      '
    profiles:
      - cli

  # Full Aztec Sandbox
  aztec-sandbox:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-sandbox-web3signer
    environment:
      NODE_NO_WARNINGS: 1
      LOG_LEVEL: info
      # Required environment variables
      PRIVATE_KEY: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
      MNEMONIC: "test test test test test test test test test test test junk"
      COINBASE_ADDRESS: "******************************************"
      FEE_RECIPIENT_ADDRESS: "******************************************"
    ports:
      - "8080:8080"  # PXE
      - "8545:8545"  # Ethereum
    volumes:
      - aztec_data:/root/.aztec
    entrypoint: []
    command: >
      node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js
      start --sandbox
    profiles:
      - sandbox

  # Aztec Node (for Sepolia testnet testing)
  aztec-node:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-node-web3signer
    environment:
      NODE_NO_WARNINGS: 1
      LOG_LEVEL: info
      # Network configuration for Sepolia
      NETWORK: "sepolia"
      PUBLIC_IP_ADDRESS: "127.0.0.1"
      VALIDATOR_P2P_PORT: "40400"
      L1_RPC: "http://host.docker.internal:2545"  # Your Sepolia EL node
      L1_CONSENSUS_HOST_URL: "http://host.docker.internal:2052"  # Your Sepolia CL node
      VALIDATOR_PRIVATE_KEY: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
      # Sepolia testnet addresses (you may need to update these)
      COINBASE_ADDRESS: "******************************************"
      FEE_RECIPIENT_ADDRESS: "******************************************"
      # Sepolia chain ID
      L1_CHAIN_ID: "11155111"
    ports:
      - "8080:8080"  # PXE
      - "40400:40400"  # P2P
    volumes:
      - aztec_data:/root/.aztec
    network_mode: host  # Use host network to access your local Ethereum node
    entrypoint: []
    command: >
      node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js
      start --network sepolia --p2p.p2pIp 127.0.0.1
      --p2p.p2pPort 40400 --l1-rpc-urls http://127.0.0.1:2545 --l1-consensus-host-urls http://127.0.0.1:2052
      --sequencer.validatorPrivateKey 0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866
      --node --archiver --sequencer
    profiles:
      - sepolia

  # Local Ethereum node (Anvil) - Only for local development
  ethereum:
    image: ghcr.io/foundry-rs/foundry:latest
    container_name: ethereum-anvil
    ports:
      - "8545:8545"
      - "8551:8551"
    command: >
      anvil
      --host 0.0.0.0
      --port 8545
      --chain-id 31337
      --accounts 10
      --balance 1000000
      --gas-limit ********
      --mnemonic "test test test test test test test test test test test junk"
    profiles:
      - local-dev  # Only for local development, not for Sepolia testing

volumes:
  aztec_data:
