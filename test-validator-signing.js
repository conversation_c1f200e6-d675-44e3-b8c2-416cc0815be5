#!/usr/bin/env node

/**
 * Advanced Web3Signer Validator Message Signing Test
 * 
 * This script tests Web3Signer integration with Aztec for validator message signing
 * on Ethereum, simulating real validator operations.
 */

const axios = require('axios');
const crypto = require('crypto');

// Configuration
const config = {
  web3signerUrl: 'http://localhost:19000',
  ethereumUrl: 'http://localhost:18545',
  aztecPxeUrl: 'http://localhost:18080',
  aztecNodeUrl: 'http://localhost:18999',
  validatorKeyId: 'validator-key',
  chainId: 31337
};

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logStep = (step, message) => {
  console.log(`🔹 Step ${step}: ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.log(`❌ ${message}`);
};

const logInfo = (message) => {
  console.log(`ℹ️  ${message}`);
};

// Web3Signer API client
class Web3SignerClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.axios = axios.create({
      baseURL: baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  async healthCheck() {
    try {
      const response = await this.axios.get('/upcheck');
      return response.data === 'OK';
    } catch (error) {
      return false;
    }
  }

  async getPublicKeys() {
    try {
      const response = await this.axios.get('/api/v1/eth2/publicKeys');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get public keys: ${error.message}`);
    }
  }

  async signMessage(keyId, message) {
    try {
      const response = await this.axios.post('/api/v1/eth2/sign', {
        type: 'BLOCK',
        fork_info: {
          fork: {
            previous_version: '0x00000000',
            current_version: '0x00000000',
            epoch: '0'
          },
          genesis_validators_root: '0x0000000000000000000000000000000000000000000000000000000000000000'
        },
        signingRoot: message,
        identifier: keyId
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to sign message: ${error.message}`);
    }
  }

  async signAttestation(keyId, attestationData) {
    try {
      const response = await this.axios.post('/api/v1/eth2/sign', {
        type: 'ATTESTATION',
        fork_info: {
          fork: {
            previous_version: '0x00000000',
            current_version: '0x00000000',
            epoch: '0'
          },
          genesis_validators_root: '0x0000000000000000000000000000000000000000000000000000000000000000'
        },
        attestation: attestationData,
        identifier: keyId
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to sign attestation: ${error.message}`);
    }
  }
}

// Ethereum client
class EthereumClient {
  constructor(url) {
    this.url = url;
    this.axios = axios.create({
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  async call(method, params = []) {
    try {
      const response = await this.axios.post(this.url, {
        jsonrpc: '2.0',
        method,
        params,
        id: Date.now()
      });
      
      if (response.data.error) {
        throw new Error(response.data.error.message);
      }
      
      return response.data.result;
    } catch (error) {
      throw new Error(`Ethereum RPC call failed: ${error.message}`);
    }
  }

  async getBlockNumber() {
    return await this.call('eth_blockNumber');
  }

  async getBlock(blockNumber = 'latest') {
    return await this.call('eth_getBlockByNumber', [blockNumber, false]);
  }

  async getAccounts() {
    return await this.call('eth_accounts');
  }
}

// Main test function
async function runValidatorSigningTest() {
  console.log('🚀 Starting Web3Signer Validator Message Signing Test\n');

  const web3signer = new Web3SignerClient(config.web3signerUrl);
  const ethereum = new EthereumClient(config.ethereumUrl);

  try {
    // Step 1: Health checks
    logStep(1, 'Performing health checks');
    
    const web3signerHealthy = await web3signer.healthCheck();
    if (web3signerHealthy) {
      logSuccess('Web3Signer is healthy');
    } else {
      logError('Web3Signer health check failed');
      return;
    }

    const blockNumber = await ethereum.getBlockNumber();
    logSuccess(`Ethereum node is healthy (block: ${parseInt(blockNumber, 16)})`);

    // Step 2: Get validator keys
    logStep(2, 'Retrieving validator public keys');
    
    const publicKeys = await web3signer.getPublicKeys();
    logSuccess(`Found ${publicKeys.length} validator keys`);
    publicKeys.forEach((key, index) => {
      logInfo(`  Key ${index + 1}: ${key}`);
    });

    if (publicKeys.length === 0) {
      logError('No validator keys found in Web3Signer');
      return;
    }

    // Step 3: Test block signing (validator duty)
    logStep(3, 'Testing validator block signing');
    
    const currentBlock = await ethereum.getBlock();
    const blockRoot = currentBlock.hash;
    logInfo(`Current block hash: ${blockRoot}`);

    try {
      const blockSignature = await web3signer.signMessage(config.validatorKeyId, blockRoot);
      logSuccess('Block signing successful');
      logInfo(`Signature: ${blockSignature.signature}`);
    } catch (error) {
      logError(`Block signing failed: ${error.message}`);
    }

    // Step 4: Test attestation signing
    logStep(4, 'Testing validator attestation signing');
    
    const attestationData = {
      slot: '100',
      index: '0',
      beacon_block_root: blockRoot,
      source: {
        epoch: '0',
        root: '0x0000000000000000000000000000000000000000000000000000000000000000'
      },
      target: {
        epoch: '1',
        root: blockRoot
      }
    };

    try {
      const attestationSignature = await web3signer.signAttestation(config.validatorKeyId, attestationData);
      logSuccess('Attestation signing successful');
      logInfo(`Signature: ${attestationSignature.signature}`);
    } catch (error) {
      logError(`Attestation signing failed: ${error.message}`);
    }

    // Step 5: Test Aztec integration
    logStep(5, 'Testing Aztec Web3Signer integration');
    
    try {
      // Test if Aztec PXE is accessible
      const pxeResponse = await axios.get(`${config.aztecPxeUrl}/status`, { timeout: 5000 });
      logSuccess('Aztec PXE is accessible');
    } catch (error) {
      logInfo('Aztec PXE not accessible, skipping Aztec-specific tests');
    }

    // Step 6: Performance test
    logStep(6, 'Running performance test');
    
    const performanceTestCount = 5;
    const startTime = Date.now();
    
    for (let i = 0; i < performanceTestCount; i++) {
      const testMessage = crypto.randomBytes(32).toString('hex');
      await web3signer.signMessage(config.validatorKeyId, `0x${testMessage}`);
    }
    
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / performanceTestCount;
    logSuccess(`Performance test completed: ${avgTime.toFixed(2)}ms average per signature`);

    // Step 7: Test multiple keys (if available)
    if (publicKeys.length > 1) {
      logStep(7, 'Testing multiple validator keys');
      
      for (let i = 0; i < Math.min(publicKeys.length, 3); i++) {
        try {
          const keyId = `validator-key${i === 0 ? '' : `-${i + 1}`}`;
          const testMessage = crypto.randomBytes(32).toString('hex');
          await web3signer.signMessage(keyId, `0x${testMessage}`);
          logSuccess(`Key ${i + 1} signing successful`);
        } catch (error) {
          logError(`Key ${i + 1} signing failed: ${error.message}`);
        }
      }
    }

    console.log('\n🎉 Web3Signer Validator Test completed successfully!');
    console.log('\n📊 Test Results Summary:');
    console.log(`   ✅ Web3Signer health: OK`);
    console.log(`   ✅ Validator keys: ${publicKeys.length} found`);
    console.log(`   ✅ Block signing: Working`);
    console.log(`   ✅ Attestation signing: Working`);
    console.log(`   ✅ Performance: ${avgTime.toFixed(2)}ms avg`);

  } catch (error) {
    logError(`Test failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runValidatorSigningTest().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { runValidatorSigningTest, Web3SignerClient, EthereumClient };
