# Custom Aztec Installation Script for Windows PowerShell
# This installs Aztec CLI tools using your custom Docker image with Web3Signer integration

Write-Host "🔧 Installing Custom Aztec CLI with Web3Signer Integration..." -ForegroundColor Green

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker first." -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js v20+ first." -ForegroundColor Red
    exit 1
}

# Create Aztec CLI directory
$aztecDir = "$env:USERPROFILE\.aztec"
if (!(Test-Path $aztecDir)) {
    New-Item -ItemType Directory -Path $aztecDir -Force | Out-Null
    Write-Host "📁 Created Aztec directory: $aztecDir" -ForegroundColor Blue
}

# Your custom Docker image name (replace with your actual image name)
$CUSTOM_IMAGE = "aztecprotocol/aztec:latest"  # This should be your custom built image
Write-Host "🐳 Using custom Docker image: $CUSTOM_IMAGE" -ForegroundColor Yellow

# Create aztec CLI wrapper script
$aztecScript = @"
@echo off
docker run --rm -it \
  --network host \
  -v "%cd%":/workspace \
  -w /workspace \
  $CUSTOM_IMAGE \
  node /usr/src/yarn-project/aztec/dest/bin/index.js %*
"@

$aztecScriptPath = "$aztecDir\aztec.bat"
$aztecScript | Out-File -FilePath $aztecScriptPath -Encoding ASCII
Write-Host "📝 Created aztec CLI script: $aztecScriptPath" -ForegroundColor Blue

# Create aztec-wallet CLI wrapper script
$walletScript = @"
@echo off
docker run --rm -it \
  --network host \
  -v "%cd%":/workspace \
  -w /workspace \
  $CUSTOM_IMAGE \
  node /usr/src/yarn-project/cli-wallet/dest/bin/index.js %*
"@

$walletScriptPath = "$aztecDir\aztec-wallet.bat"
$walletScript | Out-File -FilePath $walletScriptPath -Encoding ASCII
Write-Host "📝 Created aztec-wallet CLI script: $walletScriptPath" -ForegroundColor Blue

# Create aztec-nargo CLI wrapper script
$nargoScript = @"
@echo off
docker run --rm -it \
  --network host \
  -v "%cd%":/workspace \
  -w /workspace \
  $CUSTOM_IMAGE \
  /usr/src/noir/noir-repo/target/release/nargo %*
"@

$nargoScriptPath = "$aztecDir\aztec-nargo.bat"
$nargoScript | Out-File -FilePath $nargoScriptPath -Encoding ASCII
Write-Host "📝 Created aztec-nargo CLI script: $nargoScriptPath" -ForegroundColor Blue

# Add to PATH if not already there
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$aztecDir*") {
    $newPath = "$currentPath;$aztecDir"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "✅ Added $aztecDir to PATH" -ForegroundColor Green
    Write-Host "⚠️  Please restart your PowerShell session for PATH changes to take effect" -ForegroundColor Yellow
} else {
    Write-Host "✅ $aztecDir already in PATH" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Custom Aztec CLI installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Available commands:" -ForegroundColor Cyan
Write-Host "  aztec --help              # Main Aztec CLI with Web3Signer support"
Write-Host "  aztec-wallet --help       # Wallet CLI with Web3Signer account types"
Write-Host "  aztec-nargo --help        # Noir compiler"
Write-Host ""
Write-Host "Test Web3Signer integration:" -ForegroundColor Cyan
Write-Host "  aztec-wallet create-account --help | findstr web3signer"
Write-Host ""
Write-Host "Start sandbox with custom image:" -ForegroundColor Cyan
Write-Host "  aztec start --sandbox"
