#!/bin/bash

echo "🚀 Complete Web3Signer Validator Test Suite"
echo "============================================="

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed"
    exit 1
fi

# Check if axios is available
if ! node -e "require('axios')" 2>/dev/null; then
    echo "📦 Installing required Node.js dependencies..."
    npm install axios
fi

# Make scripts executable
chmod +x test-web3signer-validator.sh
chmod +x test-validator-signing.js

echo "🏗️ Phase 1: Setting up Web3Signer Validator Environment"
echo "--------------------------------------------------------"

# Run the infrastructure setup
./test-web3signer-validator.sh

# Check if setup was successful
if [ $? -ne 0 ]; then
    echo "❌ Infrastructure setup failed"
    exit 1
fi

echo ""
echo "🧪 Phase 2: Running Validator Message Signing Tests"
echo "---------------------------------------------------"

# Wait a bit for services to stabilize
sleep 10

# Run the detailed validator signing tests
node test-validator-signing.js

# Check if tests passed
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 All tests passed successfully!"
    echo ""
    echo "📋 What was tested:"
    echo "   ✅ Web3Signer health and API connectivity"
    echo "   ✅ Validator key management and retrieval"
    echo "   ✅ Ethereum block signing (validator duty)"
    echo "   ✅ Attestation signing (validator consensus)"
    echo "   ✅ Aztec Web3Signer account integration"
    echo "   ✅ Performance and reliability testing"
    echo "   ✅ Multiple validator key support"
    echo ""
    echo "🔧 Services running on:"
    echo "   - Ethereum (Anvil): http://localhost:18545"
    echo "   - Web3Signer: http://localhost:19000"
    echo "   - Aztec Node: http://localhost:18999"
    echo "   - Aztec PXE: http://localhost:18080"
    echo ""
    echo "💡 To interact manually:"
    echo "   # Check Web3Signer status"
    echo "   curl http://localhost:19000/upcheck"
    echo ""
    echo "   # List validator keys"
    echo "   curl http://localhost:19000/api/v1/eth2/publicKeys"
    echo ""
    echo "   # Create Aztec account with Web3Signer"
    echo "   docker run --rm --network web3signer-validator-test_validator-test \\"
    echo "     aztecprotocol/aztec:latest \\"
    echo "     node /usr/src/yarn-project/aztec/dest/bin/index.js create-account \\"
    echo "       --rpc-url http://aztec-pxe:8080 \\"
    echo "       --type web3signer \\"
    echo "       --web3signer-url http://web3signer:9000 \\"
    echo "       --web3signer-key-id validator-key"
    echo ""
    echo "🛑 To stop all services:"
    echo "   docker compose -f docker-compose.validator-test.yml down -v"
else
    echo ""
    echo "❌ Some tests failed. Check the output above for details."
    echo ""
    echo "🔍 Troubleshooting:"
    echo "   1. Check if all services are running:"
    echo "      docker compose -f docker-compose.validator-test.yml ps"
    echo ""
    echo "   2. Check service logs:"
    echo "      docker compose -f docker-compose.validator-test.yml logs web3signer"
    echo "      docker compose -f docker-compose.validator-test.yml logs aztec-node"
    echo ""
    echo "   3. Verify network connectivity:"
    echo "      curl http://localhost:19000/upcheck"
    echo "      curl http://localhost:18545"
    echo ""
    exit 1
fi
