#!/bin/bash

# Setup script for Web3Signer configuration on Linux

echo "🔧 Setting up Web3Signer configuration..."

# Create configuration directory
mkdir -p web3signer-config

# Create a simple test key configuration
cat > web3signer-config/test-key.yaml << 'EOF'
type: "file-raw"
privateKey: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
EOF

echo "✅ Created test key configuration: web3signer-config/test-key.yaml"

# Create Web3Signer configuration file
cat > web3signer-config/config.yaml << 'EOF'
# Web3Signer configuration
http-listen-host: "0.0.0.0"
http-listen-port: 9000
key-store-path: "/opt/web3signer/keys"
logging: "INFO"
EOF

echo "✅ Created Web3Signer config: web3signer-config/config.yaml"

# Set proper permissions
chmod 600 web3signer-config/test-key.yaml
chmod 644 web3signer-config/config.yaml

echo ""
echo "🎯 Web3Signer setup complete!"
echo ""
echo "Next steps:"
echo "1. Start Web3Signer: docker compose -f docker-compose-web3signer-simple.yml up -d web3signer"
echo "2. Test endpoint: curl http://localhost:9000/api/v1/eth2/publicKeys"
echo "3. Start Aztec CLI: docker compose -f docker-compose-web3signer-simple.yml up aztec-cli"
echo "4. Test integration: docker compose -f docker-compose-web3signer-simple.yml exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --type web3signer --web3signer-url http://localhost:9000 --web3signer-key-id test-key"
echo ""
echo "⚠️  Note: The test private key is for testing only. Use your own keys in production!"
