name: web3signer-simple
services:
  # Simple Web3Signer setup for testing
  web3signer:
    image: consensys/web3signer:latest
    container_name: web3signer-simple
    ports:
      - "9000:9000"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9000
      eth2
    restart: unless-stopped

  # Aztec CLI for testing Web3Signer integration
  aztec-cli:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-cli-web3signer-test
    environment:
      NODE_NO_WARNINGS: 1
    volumes:
      - .:/workspace
      - aztec_data:/root/.aztec
    working_dir: /workspace
    network_mode: host
    stdin_open: true
    tty: true
    entrypoint: []
    command: >
      sh -c '
        echo "🎯 Testing Aztec Web3Signer Integration";
        echo "=====================================";
        echo "";
        echo "Waiting for Web3Signer to be ready...";
        sleep 5;
        echo "";
        echo "Testing Web3Signer endpoint:";
        curl -f http://localhost:9000/api/v1/eth2/publicKeys || echo "Web3Signer not ready yet";
        echo "";
        echo "Testing Aztec Web3Signer integration:";
        node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --help | grep -i web3signer || echo "✅ Web3Signer integration found!";
        echo "";
        echo "Testing Web3Signer account creation:";
        echo "Command: node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --type web3signer --web3signer-url http://localhost:9000 --web3signer-key-id test-key";
        echo "";
        echo "🚀 Ready for manual testing!";
        tail -f /dev/null
      '
    depends_on:
      - web3signer

volumes:
  web3signer_keys:
  aztec_data:
