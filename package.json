{"name": "web3signer-validator-test", "version": "1.0.0", "description": "Web3Signer Validator Testing Suite for Aztec Integration", "main": "test-validator-signing.js", "scripts": {"test": "./run-validator-test.sh", "test:validator": "node test-validator-signing.js", "test:setup": "./test-web3signer-validator.sh", "clean": "docker compose -f docker-compose.validator-test.yml down -v && docker network prune -f"}, "dependencies": {"axios": "^1.6.0"}, "keywords": ["web3signer", "aztec", "validator", "ethereum", "blockchain", "testing"], "author": "Aztec Web3Signer Integration", "license": "MIT"}