name: aztec-sepolia-linux
services:
  # CLI-only service for testing Web3Signer commands on Linux
  aztec-cli:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-cli-sepolia
    environment:
      NODE_NO_WARNINGS: 1
    volumes:
      - .:/workspace
      - aztec_data:/root/.aztec
    working_dir: /workspace
    network_mode: host
    stdin_open: true
    tty: true
    entrypoint: []
    command: >
      sh -c '
        echo "🎯 Aztec CLI with Web3Signer Integration (Linux/Sepolia)";
        echo "======================================================";
        echo "";
        echo "Environment: Linux Server with Sepolia Node";
        echo "EL Port: 2545, CL Port: 2052";
        echo "";
        echo "Testing Web3Signer integration:";
        node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --help | grep -i web3signer || echo "✅ Web3Signer integration found!";
        echo "";
        echo "Available CLI commands:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js --help";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js --help";
        echo "";
        echo "Web3Signer account creation test:";
        echo "  docker compose exec aztec-cli node --no-warnings /usr/src/yarn-project/cli-wallet/dest/bin/index.js create-account --type web3signer --web3signer-url http://localhost:9000 --web3signer-key-id test-key";
        echo "";
        echo "🚀 CLI Ready for Linux/Sepolia testing!";
        tail -f /dev/null
      '
    profiles:
      - cli

  # Aztec Node connected to your Sepolia node
  aztec-sepolia:
    image: stonemac65/aztec-integration:web3signer-v1
    container_name: aztec-sepolia-node
    environment:
      NODE_NO_WARNINGS: 1
      LOG_LEVEL: info
      # Sepolia network configuration
      NETWORK: "sepolia"
      PUBLIC_IP_ADDRESS: "127.0.0.1"
      VALIDATOR_P2P_PORT: "40400"
      # Your Sepolia node endpoints
      L1_RPC: "http://127.0.0.1:2545"
      L1_CONSENSUS_HOST_URL: "http://127.0.0.1:2052"
      # Sepolia chain configuration
      L1_CHAIN_ID: "********"
      # Validator configuration (use your actual Sepolia private key)
      VALIDATOR_PRIVATE_KEY: "0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866"
      # Fee recipient (update with your actual Sepolia address)
      COINBASE_ADDRESS: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266"
      FEE_RECIPIENT_ADDRESS: "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266"
      # Aztec contract addresses on Sepolia (these may need to be updated)
      ROLLUP_CONTRACT_ADDRESS: ""  # Add actual Sepolia rollup contract address
      REGISTRY_CONTRACT_ADDRESS: ""  # Add actual Sepolia registry contract address
    ports:
      - "8080:8080"  # PXE
      - "40400:40400"  # P2P
    volumes:
      - aztec_data:/root/.aztec
    network_mode: host  # Use host network to access local Sepolia node
    entrypoint: []
    command: >
      node --no-warnings /usr/src/yarn-project/aztec/dest/bin/index.js
      start --network sepolia --p2p.p2pIp 127.0.0.1
      --p2p.p2pPort 40400 --l1-rpc-urls http://127.0.0.1:2545 --l1-consensus-host-urls http://127.0.0.1:2052
      --sequencer.validatorPrivateKey 0x25295f0d1d592a90b333e26e85149708208e9f8e8bc18f6c77bd62f8ad7a6866
      --node --archiver --sequencer
    profiles:
      - sepolia

  # Web3Signer service for testing (optional)
  web3signer:
    image: consensys/web3signer:latest
    container_name: web3signer-test
    environment:
      WEB3SIGNER_HTTP_LISTEN_HOST: "0.0.0.0"
      WEB3SIGNER_HTTP_LISTEN_PORT: "9000"
    ports:
      - "9000:9000"
    volumes:
      - web3signer_keys:/opt/web3signer/keys
      - ./web3signer-config:/opt/web3signer/config
    command: >
      --key-store-path=/opt/web3signer/keys
      --http-listen-host=0.0.0.0
      --http-listen-port=9000
      eth2
    profiles:
      - web3signer

volumes:
  aztec_data:
  web3signer_keys:
